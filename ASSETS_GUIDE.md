# Hướng dẫn Upload Assets cho Match 3 Game

## 📁 C<PERSON>u trúc thư mục Assets

Game sử dụng thư mục `public/assets/` để lưu trữ tất cả assets:

```
public/
├── assets/
│   ├── images/          ← Upload gem images vào đây
│   │   ├── gem_red.webp
│   │   ├── gem_blue.webp
│   │   ├── gem_green.webp
│   │   ├── gem_yellow.webp
│   │   ├── gem_purple.webp
│   │   ├── gem_orange.webp
│   │   └── gem_white.webp
│   ├── logo.png
│   └── bg.png
```

## 🎨 Yêu cầu cho Gem Images

### Format và kích thước:
- **Format**: `.webp` (theo PRD đã cập nhật)
- **Kích thước**: 128x128 pixels
- **Background**: Transparent (alpha channel)
- **Quality**: High resolution cho sharp graphics

### Tên file bắt buộc:
1. `gem_red.webp` - Gem màu đỏ
2. `gem_blue.webp` - Gem màu xanh dương  
3. `gem_green.webp` - Gem màu xanh lá
4. `gem_yellow.webp` - Gem màu vàng
5. `gem_purple.webp` - Gem màu tím
6. `gem_orange.webp` - Gem màu cam
7. `gem_white.webp` - Gem màu trắng

## 🔄 Cách thức hoạt động

### Automatic Fallback System:
1. **Ưu tiên**: Game sẽ tự động load custom images từ `public/assets/images/`
2. **Fallback**: Nếu không tìm thấy custom images, game sẽ tự động tạo placeholder gems
3. **Hot Reload**: Khi bạn thêm images mới, chỉ cần refresh browser

### Console Logging:
- Game sẽ log ra console: "Using custom gem images" hoặc "Using generated gem assets"
- Nếu có file nào load lỗi, sẽ hiển thị: "Failed to load: [filename]"

## 📝 Hướng dẫn Upload

### Bước 1: Chuẩn bị images
- Tạo 7 gem images theo yêu cầu trên
- Convert sang format .webp
- Đảm bảo kích thước 128x128px

### Bước 2: Upload
- Copy tất cả gem images vào thư mục: `public/assets/images/`
- Đảm bảo tên file chính xác (gem_red.webp, gem_blue.webp, etc.)

### Bước 3: Test
- Refresh browser (F5)
- Kiểm tra console để xem message
- Game sẽ tự động sử dụng custom images

## 🎯 Tips cho thiết kế Gems

### Visual Design:
- **Distinctive Colors**: Mỗi gem cần có màu sắc rõ ràng, dễ phân biệt
- **Consistent Style**: Tất cả gems nên có cùng style (cartoon, realistic, etc.)
- **Clear Shapes**: Hình dạng rõ ràng, dễ nhận biết khi scale nhỏ
- **Good Contrast**: Đảm bảo gems nổi bật trên background

### Technical:
- **Transparent Background**: Sử dụng alpha channel
- **Centered Design**: Gem nên ở giữa canvas 128x128
- **Padding**: Để một chút space xung quanh gem (khoảng 10-15px)
- **Anti-aliasing**: Smooth edges cho professional look

## 🔧 Troubleshooting

### Nếu images không hiển thị:
1. Kiểm tra tên file có chính xác không
2. Kiểm tra format có phải .webp không  
3. Kiểm tra kích thước có đúng 128x128px không
4. Xem console có error message không
5. Hard refresh (Ctrl+F5)

### Nếu muốn thay đổi format:
- Có thể sửa code trong `Preloader.ts` để support .png hoặc .jpg
- Tìm dòng: `'images/gem_red.webp'` và thay đổi extension

## 🚀 Next Steps

Sau khi upload xong gem images:
1. Game sẽ tự động sử dụng custom assets
2. Có thể tiếp tục với Phase 2: Gameplay Logic improvements
3. Thêm sound effects và background music
4. Implement special gems và power-ups

## 📞 Support

Nếu có vấn đề gì với assets, hãy:
1. Kiểm tra console messages
2. Verify file paths và naming
3. Test với một gem image trước khi upload tất cả
