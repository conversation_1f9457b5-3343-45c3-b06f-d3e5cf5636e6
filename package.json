{"name": "template-bun", "description": "A Phaser 3 template using bun.", "version": "1.1.0", "repository": {"type": "git", "url": "git+https://github.com/phaserjs/template-bun.git"}, "author": "Phaser Studio <<EMAIL>> (https://phaser.io/)", "license": "MIT", "licenseUrl": "http://www.opensource.org/licenses/mit-license.php", "bugs": {"url": "https://github.com/phaserjs/template-bun/issues"}, "homepage": "https://github.com/phaserjs/template-bun#readme", "scripts": {"dev": "bun log.js dev && bunx --bun vite --config vite/config.dev.mjs", "build": "bun log.js build && bunx --bun vite build --config vite/config.prod.mjs", "dev-nolog": "bunx --bun vite --config vite/config.dev.mjs", "build-nolog": "bunx --bun vite build --config vite/config.prod.mjs"}, "devDependencies": {"typescript": "5.9.2", "vite": "^7.1.2", "terser": "^5.43.1"}, "dependencies": {"phaser": "^3.90.0"}}