import { Scene } from 'phaser';
import { GameBoard } from '../../managers/GameBoard';

export class Game extends Scene
{
    private gameBoard: GameBoard;
    private score: number = 0;
    private scoreText: Phaser.GameObjects.Text;

    constructor ()
    {
        super('Game');
    }

    create ()
    {
        // Tạo background
        this.add.image(512, 384, 'background');

        // Tạo UI
        this.createUI();

        // Tạo game board
        this.gameBoard = new GameBoard(this);

        // Setup input
        this.setupInput();
    }

    private createUI(): void {
        // Score display
        this.scoreText = this.add.text(50, 50, 'Score: 0', {
            fontFamily: 'Arial',
            fontSize: '32px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 4
        });

        // Title
        const title = this.add.text(512, 50, 'Match 3 Game', {
            fontFamily: 'Arial Black',
            fontSize: '48px',
            color: '#ffff00',
            stroke: '#000000',
            strokeThickness: 6
        });
        title.setOrigin(0.5, 0);

        // Instructions
        const instructions = this.add.text(512, 700, 'Click gems to select, then click adjacent gem to swap', {
            fontFamily: 'Arial',
            fontSize: '20px',
            color: '#cccccc',
            align: 'center'
        });
        instructions.setOrigin(0.5);
    }

    private setupInput(): void {
        // ESC để về menu
        this.input.keyboard?.on('keydown-ESC', () => {
            this.scene.start('MainMenu');
        });
    }

    public addScore(points: number): void {
        this.score += points;
        this.scoreText.setText(`Score: ${this.score}`);
    }

    shutdown(): void {
        if (this.gameBoard) {
            this.gameBoard.destroy();
        }
    }
}
