import { Scene } from 'phaser';
import { AssetGenerator } from '../../utils/AssetGenerator';

export class Preloader extends Scene
{
    constructor ()
    {
        super('Preloader');
    }

    init ()
    {
        //  We loaded this image in our Boot Scene, so we can display it here
        this.add.image(512, 384, 'background');

        //  A simple progress bar. This is the outline of the bar.
        this.add.rectangle(512, 384, 468, 32).setStrokeStyle(1, 0xffffff);

        //  This is the progress bar itself. It will increase in size from the left based on the % of progress.
        const bar = this.add.rectangle(512-230, 384, 4, 28, 0xffffff);

        //  Use the 'progress' event emitted by the LoaderPlugin to update the loading bar
        this.load.on('progress', (progress: number) => {

            //  Update the progress bar (our bar is 464px wide, so 100% = 464px)
            bar.width = 4 + (460 * progress);

        });
    }

    preload ()
    {
        //  Set path to assets folder
        this.load.setPath('assets');

        // Load existing assets
        this.load.image('logo', 'logo.png');

        // Try to load custom gem images (webp format as per PRD)
        // If these don't exist, we'll fallback to generated assets
        this.load.image('gem_red_custom', 'images/gem_red.webp');
        this.load.image('gem_blue_custom', 'images/gem_blue.webp');
        this.load.image('gem_green_custom', 'images/gem_green.webp');
        this.load.image('gem_yellow_custom', 'images/gem_yellow.webp');
        this.load.image('gem_purple_custom', 'images/gem_purple.webp');
        this.load.image('gem_orange_custom', 'images/gem_orange.webp');
        this.load.image('gem_white_custom', 'images/gem_white.webp');

        // Handle load errors gracefully
        this.load.on('loaderror', (file: any) => {
            console.log('Failed to load:', file.key);
        });
    }

    create ()
    {
        // Check if custom images were loaded successfully
        const customImagesLoaded = this.checkCustomImagesLoaded();

        if (customImagesLoaded) {
            console.log('Using custom gem images');
            // Create aliases for custom images
            this.createCustomImageAliases();
        } else {
            console.log('Using generated gem assets');
            // Generate fallback assets
            AssetGenerator.createAllGemTextures(this);
        }

        // Always generate these assets
        AssetGenerator.createBackgroundTexture(this);
        AssetGenerator.createGridCellTexture(this);
        AssetGenerator.createSelectionTexture(this);
        AssetGenerator.createParticleTexture(this);

        //  Move to the MainMenu
        this.scene.start('MainMenu');
    }

    private checkCustomImagesLoaded(): boolean {
        const gemTypes = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'white'];
        return gemTypes.every(type =>
            this.textures.exists(`gem_${type}_custom`)
        );
    }

    private createCustomImageAliases(): void {
        const gemTypes = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'white'];
        gemTypes.forEach(type => {
            // Create alias from custom image to expected gem texture name
            const customTexture = this.textures.get(`gem_${type}_custom`);
            if (customTexture && customTexture.source[0]) {
                const source = customTexture.source[0];
                this.textures.addImage(`gem_${type}`, source.image as HTMLImageElement);
            }
        });
    }
}
