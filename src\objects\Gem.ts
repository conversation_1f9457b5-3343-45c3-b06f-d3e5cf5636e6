/**
 * Class đại diện cho một viên gem trong game
 */
export class Gem extends Phaser.GameObjects.Sprite {
    public gemType: number;
    public gridX: number;
    public gridY: number;
    public isSelected: boolean = false;
    public isMatched: boolean = false;
    public isMoving: boolean = false;
    
    private selectionGlow?: Phaser.GameObjects.Sprite;
    
    constructor(scene: Phaser.Scene, x: number, y: number, gemType: number, gridX: number, gridY: number) {
        const gemTextures = [
            'gem_red', 'gem_blue', 'gem_green', 'gem_yellow', 
            'gem_purple', 'gem_orange', 'gem_white'
        ];
        
        super(scene, x, y, gemTextures[gemType]);
        
        this.gemType = gemType;
        this.gridX = gridX;
        this.gridY = gridY;
        
        // Thêm vào scene
        scene.add.existing(this);
        
        // Set interactive
        this.setInteractive();
        this.setupInteractions();
        
        // Scale để fit grid
        this.setScale(0.8);
        this.setOrigin(0.5);
    }
    
    /**
     * Setup các tương tác chuột/touch
     */
    private setupInteractions(): void {
        // Hover effects
        this.on('pointerover', () => {
            if (!this.isSelected && !this.isMoving) {
                this.setTint(0xcccccc);
            }
        });
        
        this.on('pointerout', () => {
            if (!this.isSelected && !this.isMoving) {
                this.clearTint();
            }
        });
        
        // Click/touch
        this.on('pointerdown', () => {
            this.scene.events.emit('gem-clicked', this);
        });
    }
    
    /**
     * Hiển thị selection
     */
    public select(): void {
        if (this.isSelected) return;
        
        this.isSelected = true;
        
        // Tạo glow effect
        this.selectionGlow = this.scene.add.sprite(this.x, this.y, 'selection');
        this.selectionGlow.setDepth(this.depth - 1);
        
        // Animation pulse
        this.scene.tweens.add({
            targets: this.selectionGlow,
            scaleX: 1.2,
            scaleY: 1.2,
            alpha: 0.7,
            duration: 500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Gem scale up slightly
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.9,
            scaleY: 0.9,
            duration: 200,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Bỏ selection
     */
    public deselect(): void {
        if (!this.isSelected) return;
        
        this.isSelected = false;
        this.clearTint();
        
        // Remove glow
        if (this.selectionGlow) {
            this.scene.tweens.killTweensOf(this.selectionGlow);
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }
        
        // Reset scale
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.8,
            scaleY: 0.8,
            duration: 200,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Di chuyển gem đến vị trí mới
     */
    public moveTo(newGridX: number, newGridY: number, worldX: number, worldY: number, duration: number = 300): Promise<void> {
        return new Promise((resolve) => {
            this.isMoving = true;
            this.gridX = newGridX;
            this.gridY = newGridY;
            
            this.scene.tweens.add({
                targets: this,
                x: worldX,
                y: worldY,
                duration: duration,
                ease: 'Power2.easeOut',
                onComplete: () => {
                    this.isMoving = false;
                    resolve();
                }
            });
        });
    }
    
    /**
     * Animation khi gem bị match
     */
    public playMatchAnimation(): Promise<void> {
        return new Promise((resolve) => {
            this.isMatched = true;
            
            // Scale up và fade out
            this.scene.tweens.add({
                targets: this,
                scaleX: 1.2,
                scaleY: 1.2,
                alpha: 0,
                duration: 300,
                ease: 'Back.easeIn',
                onComplete: () => {
                    resolve();
                }
            });
            
            // Particle effect
            this.createMatchParticles();
        });
    }
    
    /**
     * Tạo particle effects khi match
     */
    private createMatchParticles(): void {
        const particles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 50, max: 150 },
            scale: { start: 0.5, end: 0 },
            lifespan: 600,
            quantity: 8
        });
        
        // Tự động destroy particles
        this.scene.time.delayedCall(600, () => {
            particles.destroy();
        });
    }
    
    /**
     * Reset gem về trạng thái ban đầu
     */
    public reset(): void {
        this.isSelected = false;
        this.isMatched = false;
        this.isMoving = false;
        this.setAlpha(1);
        this.setScale(0.8);
        this.clearTint();
        
        if (this.selectionGlow) {
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }
    }
    
    /**
     * Cleanup khi destroy
     */
    public destroy(): void {
        if (this.selectionGlow) {
            this.selectionGlow.destroy();
        }
        super.destroy();
    }
}
