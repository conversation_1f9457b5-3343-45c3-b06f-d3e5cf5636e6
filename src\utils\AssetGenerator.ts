/**
 * Utility class để tạo placeholder assets cho gems
 * Sẽ được thay thế bằng custom images sau
 */
export class AssetGenerator {
    
    /**
     * Tạo texture cho gem với màu sắc khác nhau
     */
    static createGemTexture(scene: Phaser.Scene, color: number, type: string): void {
        const graphics = scene.add.graphics();
        
        // Tạo hình kim cương
        graphics.fillStyle(color);
        graphics.beginPath();
        graphics.moveTo(64, 20);  // Top
        graphics.lineTo(100, 64); // Right
        graphics.lineTo(64, 108); // Bottom
        graphics.lineTo(28, 64);  // Left
        graphics.closePath();
        graphics.fillPath();
        
        // Thêm highlight
        graphics.fillStyle(0xffffff, 0.3);
        graphics.beginPath();
        graphics.moveTo(64, 20);
        graphics.lineTo(80, 40);
        graphics.lineTo(64, 60);
        graphics.lineTo(48, 40);
        graphics.closePath();
        graphics.fillPath();
        
        // Tạo texture từ graphics
        graphics.generateTexture(type, 128, 128);
        graphics.destroy();
    }
    
    /**
     * <PERSON><PERSON><PERSON> tất cả gem textures
     */
    static createAllGemTextures(scene: Phaser.Scene): void {
        const gemTypes = [
            { type: 'gem_red', color: 0xff4444 },
            { type: 'gem_blue', color: 0x4444ff },
            { type: 'gem_green', color: 0x44ff44 },
            { type: 'gem_yellow', color: 0xffff44 },
            { type: 'gem_purple', color: 0xff44ff },
            { type: 'gem_orange', color: 0xff8844 },
            { type: 'gem_white', color: 0xffffff }
        ];
        
        gemTypes.forEach(gem => {
            this.createGemTexture(scene, gem.color, gem.type);
        });
    }
    
    /**
     * Tạo background texture
     */
    static createBackgroundTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Gradient background
        graphics.fillGradientStyle(0x1a1a2e, 0x16213e, 0x0f3460, 0x533483, 1);
        graphics.fillRect(0, 0, 1024, 768);
        
        graphics.generateTexture('background', 1024, 768);
        graphics.destroy();
    }
    
    /**
     * Tạo grid cell texture
     */
    static createGridCellTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Cell background
        graphics.fillStyle(0x2a2a3e, 0.8);
        graphics.fillRoundedRect(2, 2, 60, 60, 8);
        
        // Border
        graphics.lineStyle(2, 0x4a4a6e, 0.6);
        graphics.strokeRoundedRect(2, 2, 60, 60, 8);
        
        graphics.generateTexture('grid_cell', 64, 64);
        graphics.destroy();
    }
    
    /**
     * Tạo selection highlight texture
     */
    static createSelectionTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Glowing border
        graphics.lineStyle(4, 0xffff00, 1);
        graphics.strokeRoundedRect(0, 0, 64, 64, 8);
        
        // Inner glow
        graphics.lineStyle(2, 0xffffff, 0.8);
        graphics.strokeRoundedRect(2, 2, 60, 60, 6);
        
        graphics.generateTexture('selection', 64, 64);
        graphics.destroy();
    }
    
    /**
     * Tạo particle texture cho effects
     */
    static createParticleTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Small sparkle
        graphics.fillStyle(0xffffff);
        graphics.fillCircle(8, 8, 6);
        
        // Inner bright spot
        graphics.fillStyle(0xffff88);
        graphics.fillCircle(8, 8, 3);
        
        graphics.generateTexture('particle', 16, 16);
        graphics.destroy();
    }
}
